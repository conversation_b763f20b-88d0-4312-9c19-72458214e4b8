version: '3.8'

services:
  ugs-ctp4d:
    image: harbor.unionstrongtech.com/ugs/ctp4d:0.1.1
    container_name: ugs-ctp4d
    working_dir: /app
    volumes:
      - /data/ctpdata:/data/ctpdata
      # 添加X11套接字映射以支持GPU渲染
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      # 添加NVIDIA设备文件映射
      - /dev/dri:/dev/dri:rw
    ports:
      - "8085:8085"
      - "8006:8006"
    command: python examples/CTP4D.py --server --host 0.0.0.0 --port 8085
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - APP_HOST=0.0.0.0
      - APP_PORT=8085
      - DATA_PATH=/data/ctpdata
      # GPU渲染相关环境变量
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=graphics,utility,compute
      - DISPLAY=${DISPLAY:-:0}
      # OpenGL相关环境变量
      - LIBGL_ALWAYS_INDIRECT=0
      - LIBGL_ALWAYS_SOFTWARE=0
      - __GLX_VENDOR_LIBRARY_NAME=nvidia
      # VTK GPU渲染环境变量
      - VTK_USE_OPENGL2=1
      - VTK_DEFAULT_RENDER_WINDOW_OFFSCREEN=1
      # GPU渲染优化
      - MESA_GL_VERSION_OVERRIDE=4.5
      - MESA_GLSL_VERSION_OVERRIDE=450
      # 强制使用NVIDIA OpenGL
      - __GL_SYNC_TO_VBLANK=0
      - __GL_YIELD=NOTHING
      # 禁用X11相关
      - QT_QPA_PLATFORM=offscreen
      - XVFB_RUN=
    stdin_open: true
    tty: true
    restart: unless-stopped
    # 启用GPU运行时支持
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu, graphics, utility, compute]

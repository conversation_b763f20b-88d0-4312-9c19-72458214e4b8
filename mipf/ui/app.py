from mipf.core.render_window import *
from mipf.core.data import *
from mipf.core.utils import *
from mipf.core.settings import *
from mipf.ui.data import *
from mipf.ui.engine import *
from abc import ABC, abstractmethod

class AppBase(ABC):
    def __init__(self, server, app_name="Undefined"):
        self.server = server
        self.app_name = app_name
        self.data_storage = DataStorage()

    @abstractmethod
    def setupui(self):
        pass

    @property
    def state(self):
        return self.server.state

    @property
    def ctrl(self):
        return self.server.controller

    def clear_all_data(self):
        """清空所有数据节点"""
        print("🧹 开始清空所有数据...")

        # 获取所有节点ID的副本，避免在迭代时修改字典
        node_ids = list(self.data_storage.nodes.keys())
        print(f"   - 找到 {len(node_ids)} 个数据节点")

        # 逐个删除节点
        for node_id in node_ids:
            if node_id in self.data_storage.nodes:
                self.data_storage.remove_node(node_id)

        # 强制清理VTK对象和GPU显存
        try:
            import gc
            import vtk

            # 强制Python垃圾回收
            gc.collect()

            # 清理VTK对象缓存
            vtk.vtkObject.GlobalWarningDisplayOff()

            # 获取所有渲染窗口并清理
            for render_window in render_window_manager.render_windows:
                if hasattr(render_window, 'get_vtk_render_window'):
                    vtk_render_window = render_window.get_vtk_render_window()
                    if vtk_render_window:
                        # 清理渲染器中的所有对象
                        renderers = vtk_render_window.GetRenderers()
                        renderers.InitTraversal()
                        renderer = renderers.GetNextItem()
                        while renderer:
                            renderer.RemoveAllViewProps()
                            renderer.Clear()
                            renderer = renderers.GetNextItem()

                        # 强制渲染一次空场景以清理GPU缓存
                        vtk_render_window.Render()

            print("   - VTK对象和GPU显存清理完成")

        except Exception as e:
            print(f"   - VTK清理过程中出现警告: {str(e)}")

        # 请求更新渲染
        render_window_manager.request_update_all()
        if self.server.protocol:
            self.ctrl.view_update()

        print("✅ 数据清空完成")

    def get_supported_extensions(self):
        """获取支持的文件扩展名列表"""
        return ['.nii', '.nii.gz', '.vti', '.mha', '.nrrd', '.vtp', '.stl']

    def is_supported_file(self, file_path: str):
        """检查文件是否为支持的格式"""
        file_path_lower = file_path.lower()
        return any(file_path_lower.endswith(ext) for ext in self.get_supported_extensions())

    def scan_folder_for_files(self, folder_path: str, recursive=False):
        """扫描文件夹中的支持格式文件"""
        import os

        supported_files = []

        if not os.path.isdir(folder_path):
            return supported_files

        try:
            if recursive:
                # 递归扫描子文件夹
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if self.is_supported_file(file_path):
                            supported_files.append(file_path)
            else:
                # 只扫描当前文件夹
                for item in os.listdir(folder_path):
                    item_path = os.path.join(folder_path, item)
                    if os.path.isfile(item_path) and self.is_supported_file(item_path):
                        supported_files.append(item_path)

            # 按文件名排序
            supported_files.sort()

        except Exception as e:
            print(f"扫描文件夹失败: {folder_path}, 错误: {str(e)}")

        return supported_files

    def load_data_from_path_or_folder(self, path: str, clear_existing=True, recursive=False):
        """从文件路径或文件夹路径加载数据"""
        import os

        if not os.path.exists(path):
            return {
                'success': False,
                'message': f'路径不存在: {path}',
                'loaded_files': [],
                'failed_files': []
            }

        if clear_existing:
            self.clear_all_data()

        loaded_files = []
        failed_files = []

        if os.path.isfile(path):
            # 单个文件
            if self.is_supported_file(path):
                try:
                    node_name = os.path.basename(path)
                    self.load(path, node_name)
                    loaded_files.append(path)
                    print(f"成功加载文件: {path}")
                except Exception as e:
                    failed_files.append({'path': path, 'error': str(e)})
                    print(f"加载文件失败: {path}, 错误: {str(e)}")
            else:
                return {
                    'success': False,
                    'message': f'不支持的文件格式: {path}',
                    'supported_formats': self.get_supported_extensions(),
                    'loaded_files': [],
                    'failed_files': []
                }

        elif os.path.isdir(path):
            # 文件夹
            supported_files = self.scan_folder_for_files(path, recursive)

            if not supported_files:
                return {
                    'success': False,
                    'message': f'文件夹中没有找到支持的文件: {path}',
                    'supported_formats': self.get_supported_extensions(),
                    'loaded_files': [],
                    'failed_files': []
                }

            print(f"在文件夹 {path} 中找到 {len(supported_files)} 个支持的文件")

            # 逐个加载文件
            for file_path in supported_files:
                try:
                    node_name = os.path.basename(file_path)
                    self.load(file_path, node_name)
                    loaded_files.append(file_path)
                    print(f"成功加载文件: {file_path}")
                except Exception as e:
                    failed_files.append({'path': file_path, 'error': str(e)})
                    print(f"加载文件失败: {file_path}, 错误: {str(e)}")

        # 更新渲染
        if loaded_files:
            render_window_manager.request_update_all()
            if self.server.protocol:
                self.ctrl.reset_camera()
                self.ctrl.view_update()

        # 构建响应
        total_files = len(loaded_files) + len(failed_files)
        success = len(loaded_files) > 0

        if success:
            if failed_files:
                message = f"部分成功：加载了 {len(loaded_files)}/{total_files} 个文件"
            else:
                message = f"全部成功：加载了 {len(loaded_files)} 个文件"
        else:
            message = f"全部失败：{len(failed_files)} 个文件加载失败"

        return {
            'success': success,
            'message': message,
            'loaded_files': loaded_files,
            'failed_files': failed_files,
            'total_found': total_files,
            'path_type': 'file' if os.path.isfile(path) else 'folder'
        }

    def load(self, filename: str, name="undefined"):
        if filename.endswith('nii') or filename.endswith('nii.gz') or \
                filename.endswith('vti') or filename.endswith('mha') or \
                filename.endswith('nrrd'):
            node = import_image_file(filename, name)
            self.data_storage.add_node(node)
            render_window_manager.request_update_all()
            if self.server.protocol:
                self.ctrl.reset_camera()
                self.ctrl.view_update()
        elif filename.endswith('vtp') or filename.endswith('stl'):
            node = import_surface_file(filename, name)
            self.data_storage.add_node(node)
            render_window_manager.request_update_all()
            if self.server.protocol:
                self.ctrl.reset_camera()
                self.ctrl.view_update()
        else:
            print("Not a supported file ",filename)